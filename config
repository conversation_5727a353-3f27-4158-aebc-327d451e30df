Host gcp-instance
    HostName ************
    User <EMAIL>
    IdentityFile ~/.ssh/google_cloud
    IdentitiesOnly yes

# Google Cloud Compute Engine with IAP tunnel (recommended)
Host gcp-saien
    HostName saien-server-standard.us-central1-c.c.fast-4bb23.internal
    User fengyangsen
    IdentityFile ~/.ssh/gcp_ed25519_key_with_passphrase
    ProxyCommand gcloud compute start-iap-tunnel saien-server-standard 22 --listen-on-stdin --project=fast-4bb23 --zone=us-central1-c --verbosity=warning
    ProxyUseFdpass no
    StrictHostKeyChecking no

# Alternative direct connection (if network issues are resolved)
Host gcp-saien-direct
    HostName ************
    User fengyangsen
    IdentityFile ~/.ssh/gcp_ed25519_key_with_passphrase
    StrictHostKeyChecking no

# Default settings
Host *
    AddKeysToAgent yes
    UseKeychain yes