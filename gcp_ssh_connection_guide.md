# Google Cloud SSH 连接指南

## 🔧 配置完成的连接方式

### 推荐方式：传统 SSH 命令通过 IAP 隧道
```bash
# 连接到 Google Cloud 实例
ssh gcp-saien

# 执行远程命令
ssh gcp-saien "whoami && hostname"

# 文件传输
scp file.txt gcp-saien:~/
scp gcp-saien:~/remote-file.txt ./
```

### 备用方式：gcloud 命令
```bash
# 通过 IAP 隧道连接
gcloud compute ssh saien-server-standard --zone=us-central1-c --tunnel-through-iap

# 直接连接（如果网络问题解决）
gcloud compute ssh saien-server-standard --zone=us-central1-c
```

## 🔑 密钥信息

- **密钥类型**: ED25519 (现代、安全)
- **密钥文件**: `~/.ssh/gcp_ed25519_key_with_passphrase`
- **密钥密码**: test123
- **用户名**: fengyangsen

## 📋 SSH 配置详情

SSH 配置文件 (`~/.ssh/config`) 已更新，包含：

1. **gcp-saien**: 通过 IAP 隧道的连接配置
2. **gcp-saien-direct**: 直接连接配置（备用）

## ⚠️ 重要说明

1. **网络问题**: 直接 SSH 连接目前存在网络层面的问题
2. **解决方案**: 使用 IAP 隧道作为可靠的连接方式
3. **安全性**: ED25519 密钥比 RSA 更安全，兼容最新的 OpenSSH

## 🔄 故障排除

如果连接失败：
1. 确保 gcloud CLI 已认证
2. 检查项目和区域设置
3. 验证密钥密码正确
4. 使用 IAP 隧道作为备用方案

## 📁 备份位置

原始配置已备份到：`~/.ssh/backup_$(date +%Y%m%d)/`
